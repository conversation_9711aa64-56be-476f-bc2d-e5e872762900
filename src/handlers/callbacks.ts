import { Context, Markup } from "telegraf";
import {
  getUserOrdersByTgId,
  formatOrderForDisplay,
  getCompletableOrders,
} from "../firebase-service";
import {
  createMarketplaceInlineKeyboard,
  createSupportKeyboard,
  createOrderActionsKeyboard,
  createOrderBackKeyboard,
  createMainKeyboard,
  createOrderCompletionKeyboard,
} from "../utils/keyboards";
import { clearSessionProperty, setUserSession } from "../services/session";

export const handleOrderHelpCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(
    "📋 Order Help\n\n" +
      "If you need help with your order:\n\n" +
      "1. Open the marketplace using the menu button\n" +
      "2. Go to your profile/orders section\n" +
      "3. Find your order and follow the instructions\n" +
      "4. Contact support if you encounter issues\n\n" +
      "For immediate assistance, you can also contact our support team.",
    createSupportKeyboard()
  );
};

export const handleContactSupportCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(
    "📞 Contact Support\n\n" +
      "You can reach our support team through:\n\n" +
      "• Email: <EMAIL>\n" +
      "• Telegram: @marketplace_support\n" +
      "• Live chat in the web app\n\n" +
      "We typically respond within 24 hours.",
    createMarketplaceInlineKeyboard()
  );
};

export const handleOpenMarketplaceCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(
    "🌐 Opening Marketplace\n\n" +
      "Click the button below to open the full marketplace experience:",
    createMarketplaceInlineKeyboard()
  );
};

export const handleCancelEchoModeCallback = (ctx: Context) => {
  ctx.answerCbQuery();

  const userId = ctx.from?.id?.toString();
  if (userId) {
    clearSessionProperty(userId, "echoMode");
  }

  ctx.reply(
    "❌ Echo Gift Mode Cancelled\n\n" +
      "You can use the menu buttons or commands to continue using the bot.",
    createMarketplaceInlineKeyboard()
  );
};

export const handleOrderSelectionCallback = async (ctx: any) => {
  try {
    ctx.answerCbQuery();

    const orderId = ctx.match[1];
    const tgId = ctx.from?.id?.toString();

    if (!tgId || !orderId) {
      ctx.reply(
        "❌ Unable to identify your Telegram ID or order. Please try again."
      );
      return;
    }

    // Get user orders to find the selected order
    const ordersResponse = await getUserOrdersByTgId(tgId);
    const order = ordersResponse.orders.find((o) => o.id === orderId);

    if (!order) {
      ctx.reply("❌ Order not found. It may have been completed or cancelled.");
      return;
    }

    let message = `📦 Order #${order.number}\n\n`;
    message += `${formatOrderForDisplay(order)}\n\n`;

    if (order.status === "paid" && order.buyerId) {
      message += "🎁 This order is ready for completion!\n\n";
      message += "To complete this order:\n";
      message += "1. Send the gift/item to this bot\n";
      message += "2. The bot will verify and complete the purchase\n";
      message += "3. Funds will be transferred to the seller\n\n";
      message +=
        "⚠️ Only send the gift when you're ready to complete the order!";

      ctx.reply(message, createOrderActionsKeyboard(orderId));
    } else {
      message += `Status: ${order.status.toUpperCase()}\n\n`;

      if (order.status === "active") {
        message += "This order is waiting for a buyer.";
      } else if (order.status === "fulfilled") {
        message += "This order has been completed.";
      } else if (order.status === "cancelled") {
        message += "This order has been cancelled.";
      }

      ctx.reply(message, createOrderBackKeyboard());
    }
  } catch (error) {
    console.error("Error handling order selection:", error);
    ctx.reply("❌ Failed to load order details. Please try again later.");
  }
};

export const handleBackToOrdersCallback = async (ctx: Context) => {
  try {
    ctx.answerCbQuery();

    const tgId = ctx.from?.id?.toString();
    if (!tgId) {
      ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
      return;
    }

    // Trigger the same logic as "Get My Orders" button
    const ordersResponse = await getUserOrdersByTgId(tgId);

    if (!ordersResponse.success || ordersResponse.orders.length === 0) {
      ctx.reply(
        "📭 You don't have any orders yet.\n\n" +
          "Create your first order using the marketplace web app!",
        createMarketplaceInlineKeyboard()
      );
      return;
    }

    const orders = ordersResponse.orders;
    const completableOrders = getCompletableOrders(orders);

    let message = `📋 Your Orders (${orders.length} total)\n\n`;

    if (completableOrders.length > 0) {
      message += `🟠 Orders ready for completion: ${completableOrders.length}\n\n`;
    }

    const orderButtons = orders
      .slice(0, 10)
      .map((order) => [
        Markup.button.callback(
          `${formatOrderForDisplay(order)}`,
          `order_${order.id}`
        ),
      ]);

    orderButtons.push([
      Markup.button.callback("🌐 Open Marketplace", "open_marketplace"),
    ]);

    if (orders.length > 10) {
      message += `\n📝 Showing first 10 orders. Use the web app to see all orders.`;
    }

    ctx.editMessageText(message, Markup.inlineKeyboard(orderButtons));
  } catch (error) {
    console.error("Error going back to orders:", error);
    ctx.reply("❌ Failed to load orders. Please try again later.");
  }
};

export const handleOrderCompletionCallback = async (ctx: any) => {
  try {
    ctx.answerCbQuery();

    const orderId = ctx.match[1];

    if (!orderId) {
      ctx.reply("❌ Unable to identify order. Please try again.");
      return;
    }

    ctx.reply(
      "🎁 Ready to Complete Order\n\n" +
        "Please send the gift/item to this bot now. You can send:\n" +
        "• Photos\n" +
        "• Documents\n" +
        "• Text messages\n" +
        "• Any other content\n\n" +
        "⚠️ Once you send the gift, the order will be automatically completed and funds will be transferred!",
      createOrderCompletionKeyboard(orderId)
    );

    // Store the order ID for the next message from this user
    const userId = ctx.from?.id?.toString();
    if (userId && orderId) {
      setUserSession(userId, { pendingOrderId: orderId });
    }
  } catch (error) {
    console.error("Error handling order completion:", error);
    ctx.reply("❌ Failed to prepare order completion. Please try again later.");
  }
};

export const handleReceiveGiftCallback = async (ctx: any) => {
  try {
    ctx.answerCbQuery();

    // const orderId = ctx.match[1];
    const userId = ctx.from?.id?.toString();

    if (!userId) {
      ctx.reply("❌ Unable to identify your Telegram ID. Please try again.");
      return;
    }

    ctx.reply("🎁 Preparing your gift for delivery...");

    // Here we would implement the actual gift delivery logic
    // For now, we'll just mark the order as fulfilled
    // In a real implementation, this would involve the actual gift transfer mechanism

    ctx.reply(
      "🎉 Gift received successfully!\n\n" +
        `📦 Order has been completed.\n` +
        "🎁 Your gift has been delivered!\n\n" +
        "Thank you for using our marketplace!",
      createOrderBackKeyboard()
    );
  } catch (error) {
    console.error("Error receiving gift:", error);
    ctx.reply(
      "❌ Failed to receive gift. Please try again later.",
      createOrderBackKeyboard()
    );
  }
};

export const handleBackToMenuCallback = (ctx: Context) => {
  ctx.answerCbQuery();
  ctx.reply(
    "🔙 Back to Menu\n\n" +
      "You can use the buttons below to navigate or open the marketplace:",
    createMainKeyboard()
  );
};
